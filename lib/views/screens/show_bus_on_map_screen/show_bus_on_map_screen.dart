import 'dart:convert';
import 'package:bus/data/models/trip_models/trip_route_model.dart';
import 'package:bus/data/repo/trip_route_repo.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:geolocator/geolocator.dart';

import '../../../config/config_base.dart';
import '../../../config/global_variable.dart';
import '../../../config/theme_colors.dart';
import '../../../constant/path_route_name.dart';
import '../../../utils/assets_utils.dart';
import '../../../widgets/custom_appbar.dart';

class ShowBusOnMapScreen extends StatefulWidget {
  static const String routeName = PathRouteName.showBusOnMap;
  final String name;
  final String busId;
  final String busName;
  final String? userableId;
  final String type;
  final String? tripId; // Add tripId parameter for fetching route

  const ShowBusOnMapScreen({
    super.key,
    required this.busId,
    required this.busName,
    required this.type,
    this.userableId,
    required this.name,
    this.tripId, // Optional tripId for fetching previous route
  });

  @override
  State<ShowBusOnMapScreen> createState() => _ShowBusOnMapScreenState();
}

class _ShowBusOnMapScreenState extends State<ShowBusOnMapScreen> {
  late final io.Socket socket;
  LatLng? previousLocation;
  LatLng? currentLocation;
  GoogleMapController? mapController;
  bool? isLocationAvailable;
  bool isLoading = true;
  bool hasError = false;
  bool isSocketConnected = false;
  bool hasReceivedFirstUpdate = false;
  DateTime? previousUpdateTime;
  double? speed;
  Set<Marker> markers = {};

  // Trip route related variables
  final TripRouteRepo _tripRouteRepo = TripRouteRepo();
  TripRoute? tripRoute;
  bool isLoadingRoute = false;
  bool showPreviousRoute = false;
  Set<Polyline> polylines = {};

  // Current bus route tracking
  List<LatLng> currentRoutePoints = [];
  bool showCurrentRoute = true;

  @override
  void initState() {
    super.initState();
    _initializeSocket();
    _initializeMapController();

    // Fetch previous route if tripId is provided
    if (widget.tripId != null) {
      _fetchPreviousRoute();
    }

    Future.delayed(const Duration(seconds: 10), () {
      if (mounted && !hasReceivedFirstUpdate) {
        setState(() {
          isLoading = false;
          isLocationAvailable = false;
        });
      }
    });
  }

  /// Fetch the previous route for the trip
  Future<void> _fetchPreviousRoute() async {
    if (widget.tripId == null) {
      debugPrint('tripId is null, cannot fetch route');
      return;
    }

    debugPrint('Fetching previous route for trip ${widget.tripId}');

    setState(() {
      isLoadingRoute = true;
    });

    try {
      final response =
          await _tripRouteRepo.getTripRoute(tripId: widget.tripId!);

      debugPrint('Route API response status: ${response.status}');
      debugPrint('Route API response message: ${response.message}');

      if (response.status == true && response.data != null) {
        debugPrint(
            'Route data received, routes count: ${response.data?.routes?.length ?? 0}');

        setState(() {
          tripRoute = response.data;
          isLoadingRoute = false;

          // Create polyline from route points
          if (tripRoute?.routes != null && tripRoute!.routes!.isNotEmpty) {
            final List<LatLng> routePoints = tripRoute!.getRouteLatLngs();

            debugPrint(
                'Route points converted to LatLng: ${routePoints.length}');

            if (routePoints.isNotEmpty) {
              // Print first and last points for debugging
              debugPrint(
                  'First point: ${routePoints.first.latitude}, ${routePoints.first.longitude}');
              debugPrint(
                  'Last point: ${routePoints.last.latitude}, ${routePoints.last.longitude}');

              // Clear existing polylines and add new one
              polylines.clear();
              polylines.add(
                Polyline(
                  polylineId: const PolylineId('previousRoute'),
                  points: routePoints,
                  color: Colors.red, // Changed to red for better visibility
                  width: 5,
                  // Removed patterns to make it a solid line
                ),
              );

              // Set showPreviousRoute to true by default
              showPreviousRoute = true;

              debugPrint(
                  'Polyline added to map, showPreviousRoute: $showPreviousRoute');

              // If we have a map controller, animate to show the route
              if (mapController != null && routePoints.isNotEmpty) {
                // Calculate bounds to fit all points
                double minLat = routePoints.first.latitude;
                double maxLat = routePoints.first.latitude;
                double minLng = routePoints.first.longitude;
                double maxLng = routePoints.first.longitude;

                for (final point in routePoints) {
                  if (point.latitude < minLat) minLat = point.latitude;
                  if (point.latitude > maxLat) maxLat = point.latitude;
                  if (point.longitude < minLng) minLng = point.longitude;
                  if (point.longitude > maxLng) maxLng = point.longitude;
                }

                // Add padding to bounds
                final LatLngBounds bounds = LatLngBounds(
                  southwest: LatLng(minLat - 0.01, minLng - 0.01),
                  northeast: LatLng(maxLat + 0.01, maxLng + 0.01),
                );

                // Animate camera to show the entire route
                mapController!.animateCamera(
                  CameraUpdate.newLatLngBounds(bounds, 50),
                );

                debugPrint('Camera animated to show route bounds');
              }
            } else {
              debugPrint('No valid route points found after conversion');
            }
          } else {
            debugPrint('No routes found in response data');
          }
        });
      } else {
        setState(() {
          isLoadingRoute = false;
        });
        debugPrint('Failed to fetch route: ${response.message}');

        // Show error message to user
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Failed to load previous route: ${response.message ?? "Unknown error"}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e, stackTrace) {
      setState(() {
        isLoadingRoute = false;
      });
      debugPrint('Error fetching route: $e');
      debugPrint('Stack trace: $stackTrace');

      // Show error message to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppStrings.errorLoadingRoute.tr()}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _initializeSocket() {
    try {
      socket = io.io(ConfigBase.socketUrl, {
        'query': {'token': socketToken},
        'transports': ['websocket'],
      });
      socket.connect();
      _setupSocketListeners();
    } catch (e) {
      debugPrint('Error initializing socket: $e');
      _showErrorSnackbar(AppStrings.failedConnectWebsocket.tr(), context);
      setState(() {
        hasError = true;
        isLoading = false;
      });
    }
  }

  void _initializeMapController() {
    try {
      const defaultLat = 31.04752;
      const defaultLng = 31.39235;

      // For Google Maps, we don't need to initialize the controller here
      // It will be initialized in the onMapCreated callback
      // Just set the initial location
      setState(() {
        currentLocation = const LatLng(defaultLat, defaultLng);
      });
    } catch (e) {
      debugPrint('Error initializing map controller: $e');
      _showErrorSnackbar('Failed to initialize the map.', context);
      setState(() {
        hasError = true;
        isLoading = false;
      });
    }
  }

  void _setupSocketListeners() {
    try {
      socket.onConnect((_) {
        debugPrint('Connected to WebSocket');
        if (mounted) {
          setState(() {
            isSocketConnected = true;
          });
        }
      });

      socket.onDisconnect((reason) {
        debugPrint('Disconnected from WebSocket: $reason');
        if (mounted) {
          setState(() {
            isSocketConnected = false;
            hasError = true;
            isLoading = false;
          });
        }
        _showErrorSnackbar('Disconnected from WebSocket.', context);
      });

      socket.on(widget.busId, _handleLocationUpdate);
    } catch (e) {
      debugPrint('Error setting up socket listeners: $e');
      _showErrorSnackbar('Failed to set up WebSocket listeners.', context);
      setState(() {
        hasError = true;
        isLoading = false;
      });
    }
  }

  void _handleLocationUpdate(dynamic data) async {
    if (!mounted) return;
    debugPrint("$data");
    try {
      final locationJson = jsonDecode(data);
      debugPrint('Received location data: $locationJson');
      final latitude =
          double.tryParse(locationJson['latitude']?.toString() ?? '');
      final longitude =
          double.tryParse(locationJson['longitude']?.toString() ?? '');
      final type = locationJson['type']?.toString();

      if (latitude == null || longitude == null || type == null) {
        debugPrint('Invalid location data received');
        if (mounted) {
          _showErrorSnackbar('Received invalid location data.', context);
          setState(() {
            isLoading = false;
            isLocationAvailable = false;
          });
        }
        return;
      }

      if (type != widget.type) {
        debugPrint('Type mismatch: received $type, expected ${widget.type}');
        if (!hasReceivedFirstUpdate && mounted) {
          setState(() {
            isLoading = false;
            isLocationAvailable = false;
          });
        }
        return;
      }

      final newLocation = LatLng(latitude, longitude);
      final now = DateTime.now();

      // Calculate speed only if we have previous data and sufficient time has passed
      if (previousLocation != null && previousUpdateTime != null) {
        final timeDifferenceInSeconds =
            now.difference(previousUpdateTime!).inMilliseconds / 1000;

        // Only calculate speed if at least 1 second has passed
        if (timeDifferenceInSeconds >= 1) {
          final distance = Geolocator.distanceBetween(
            previousLocation!.latitude,
            previousLocation!.longitude,
            newLocation.latitude,
            newLocation.longitude,
          );

          // Convert m/s to km/h
          speed = (distance / timeDifferenceInSeconds) * 3.6;

          // Set speed to 0 if it's very small (to avoid showing negligible movements)
          if (speed! < 0.5) {
            speed = 0;
          }

          // Cap maximum speed to reasonable value (e.g., 200 km/h)
          if (speed! > 200) {
            speed = 200;
          }
        }
      }

      if (mounted) {
        setState(() {
          currentLocation = newLocation;
          isLocationAvailable = true;
          isLoading = false;
          hasReceivedFirstUpdate = true;
          previousUpdateTime = now;

          // Update markers
          markers.clear();
          markers.add(
            Marker(
              markerId: const MarkerId('busLocation'),
              position: newLocation,
              infoWindow: InfoWindow(
                title: widget.busName,
                snippet: 'Speed: ${speed?.toStringAsFixed(2) ?? "N/A"} km/h',
              ),
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueBlue),
            ),
          );

          // Add current location to route points
          currentRoutePoints.add(newLocation);

          // Update current route polyline
          if (currentRoutePoints.length > 1 && showCurrentRoute) {
            // Add or update the current route polyline
            final currentRoutePolyline = Polyline(
              polylineId: const PolylineId('currentRoute'),
              points: currentRoutePoints,
              color: Colors.blue,
              width: 5,
            );

            // Remove old current route polyline if exists
            polylines.removeWhere(
                (polyline) => polyline.polylineId.value == 'currentRoute');

            // Add the updated polyline
            polylines.add(currentRoutePolyline);

            debugPrint(
                'Current route updated, points: ${currentRoutePoints.length}');
          }
        });

        // Move camera to new location
        if (mapController != null) {
          await mapController!.animateCamera(
            CameraUpdate.newLatLngZoom(newLocation, 15),
          );

          // If this is the first update and we have a trip ID, fetch the previous route
          if (!hasReceivedFirstUpdate &&
              widget.tripId != null &&
              polylines.isEmpty) {
            debugPrint(
                'First location update received, fetching previous route');
            _fetchPreviousRoute();
          }
        }
      }
    } catch (e) {
      debugPrint('Error processing location data: $e');
      if (mounted) {
        _showErrorSnackbar(
            'An error occurred while updating the location.', context);
        setState(() {
          isLoading = false;
          isLocationAvailable = false;
        });
      }
    }
  }

  // No longer needed as we're handling markers directly in _handleLocationUpdate

  void _showErrorSnackbar(String message, BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  @override
  void dispose() {
    try {
      socket.disconnect();
      socket.off(widget.busId);
    } catch (e) {
      debugPrint('Error during socket disconnection: $e');
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: const SizedBox(),
        titleWidget: Row(
          children: [
            CustomText(
              text: widget.name,
              fontSize: 18,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            const SizedBox(width: 10.0),
          ],
        ),
        leftWidget: InkWell(
          onTap: () => Navigator.pop(context),
          child: SvgPicture.asset(
            context.locale.toString() == "ar"
                ? AppAssets.arrowBack
                : AppAssets.forwardArrow,
            colorFilter: const ColorFilter.mode(TColor.white, BlendMode.srcIn),
            width: 25.w,
            height: 25.w,
          ),
        ),
      ),
      body: SafeArea(
        child: hasError
            ? Center(
                child: Card(
                  elevation: 5,
                  color: Colors.red[50],
                  child: const Padding(
                    padding: EdgeInsets.all(30.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.error,
                          color: Colors.red,
                          size: 50,
                        ),
                        SizedBox(height: 10),
                        CustomText(
                          text: "An error occurred. Please try again later.",
                          fontSize: 20,
                          fontW: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ],
                    ),
                  ),
                ),
              )
            : isLoading
                ? const Center(
                    child: CircularProgressIndicator(),
                  )
                : isLocationAvailable != true
                    ? Center(
                        child: Card(
                          elevation: 5,
                          color: Colors.yellow[50],
                          child: const Padding(
                            padding: EdgeInsets.all(30.0),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  color: Colors.yellow,
                                  size: 50,
                                ),
                                SizedBox(height: 10),
                                CustomText(
                                  text: "No Trips Right Now",
                                  fontSize: 20,
                                  fontW: FontWeight.w600,
                                  color: Colors.black87,
                                ),
                              ],
                            ),
                          ),
                        ),
                      )
                    : Stack(
                        children: [
                          GoogleMap(
                            initialCameraPosition: CameraPosition(
                              target: LatLng(
                                currentLocation?.latitude ?? 0.0,
                                currentLocation?.longitude ?? 0.0,
                              ),
                              zoom: 15,
                            ),
                            onMapCreated: (GoogleMapController controller) {
                              mapController = controller;
                            },
                            markers: markers,
                            polylines: showPreviousRoute ? polylines : {},
                          ),
                          // Info card at the top
                          if (currentLocation != null)
                            Positioned(
                              top: 20,
                              left: 20,
                              right: 20,
                              child: Card(
                                elevation: 4,
                                color: Colors.white,
                                child: Padding(
                                  padding: const EdgeInsets.all(12.0),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      CustomText(
                                        text:
                                            "Bus Name: ${widget.busName}, Speed: ${speed != null ? speed!.toStringAsFixed(2) : 'N/A'} km/h",
                                        fontSize: 16,
                                        fontW: FontWeight.w400,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),

                          // Previous route toggle button
                          if (polylines.isNotEmpty && tripRoute != null)
                            Positioned(
                              bottom: 20,
                              right: 20,
                              child: FloatingActionButton(
                                heroTag: "togglePreviousRouteBtn",
                                backgroundColor: showPreviousRoute
                                    ? Colors.red
                                    : Colors.grey,
                                onPressed: () {
                                  setState(() {
                                    showPreviousRoute = !showPreviousRoute;
                                  });
                                },
                                tooltip: showPreviousRoute
                                    ? 'Hide Previous Route'
                                    : 'Show Previous Route',
                                child: Icon(
                                  Icons.history,
                                  color: Colors.white,
                                ),
                              ),
                            ),

                          // Current route toggle button
                          if (currentRoutePoints.length > 1)
                            Positioned(
                              bottom: 20,
                              right:
                                  90, // Position to the left of the previous route button
                              child: FloatingActionButton(
                                heroTag: "toggleCurrentRouteBtn",
                                backgroundColor: showCurrentRoute
                                    ? Colors.blue
                                    : Colors.grey,
                                onPressed: () {
                                  setState(() {
                                    showCurrentRoute = !showCurrentRoute;

                                    if (showCurrentRoute) {
                                      // Re-add the current route polyline
                                      if (currentRoutePoints.length > 1) {
                                        final currentRoutePolyline = Polyline(
                                          polylineId:
                                              const PolylineId('currentRoute'),
                                          points: currentRoutePoints,
                                          color: Colors.blue,
                                          width: 5,
                                        );

                                        // Remove old current route polyline if exists
                                        polylines.removeWhere((polyline) =>
                                            polyline.polylineId.value ==
                                            'currentRoute');

                                        // Add the updated polyline
                                        polylines.add(currentRoutePolyline);
                                      }
                                    } else {
                                      // Remove the current route polyline
                                      polylines.removeWhere((polyline) =>
                                          polyline.polylineId.value ==
                                          'currentRoute');
                                    }
                                  });
                                },
                                tooltip: showCurrentRoute
                                    ? 'Hide Current Route'
                                    : 'Show Current Route',
                                child: Icon(
                                  Icons.timeline,
                                  color: Colors.white,
                                ),
                              ),
                            ),

                          // Loading indicator for route
                          if (isLoadingRoute)
                            Positioned(
                              bottom: 80,
                              right: 20,
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(10),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.1),
                                      blurRadius: 5,
                                      offset: const Offset(0, 3),
                                    ),
                                  ],
                                ),
                                child: const Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                Colors.blue),
                                      ),
                                    ),
                                    SizedBox(width: 8),
                                    CustomText(
                                      text: "Loading route...",
                                      fontSize: 14,
                                      fontW: FontWeight.w400,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                        ],
                      ),
      ),
    );
  }
}
