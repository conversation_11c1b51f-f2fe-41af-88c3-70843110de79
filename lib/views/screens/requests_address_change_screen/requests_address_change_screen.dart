import 'package:bus/bloc/change_address_cubit/change_address_cubit.dart';
import 'package:bus/bloc/change_address_cubit/change_address_states.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/utils/helpers.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../config/theme_colors.dart';
import 'package:bus/translations/local_keys.g.dart';
import '../../../utils/assets_utils.dart';
import '../../../widgets/custom_appbar.dart';
import '../../../widgets/page_number_widget.dart';
import '../../custom_widgets/build_table_row_widget.dart';
import '../../custom_widgets/custom_text.dart';

class RequestsAddressChangeScreen extends StatefulWidget {
  final String? status;
  final String? appBarTitle;
  final bool hideAppBar;
  static const String routeName = PathRouteName.requestsAddressChange;

  const RequestsAddressChangeScreen({
    Key? key,
    this.status,
    this.appBarTitle,
    this.hideAppBar = false,
  }) : super(key: key);

  @override
  State<RequestsAddressChangeScreen> createState() =>
      _RequestsAddressChangeScreenState();
}

class _RequestsAddressChangeScreenState
    extends State<RequestsAddressChangeScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: widget.hideAppBar
          ? null
          : CustomAppBar(
              titleWidget: CustomText(
                text: widget.appBarTitle,
                fontSize: 18,
                textAlign: TextAlign.center,
                fontW: FontWeight.w600,
                color: TColor.white,
              ),
              leftWidget: context.locale.toString() == "ar"
                  ? InkWell(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: SvgPicture.asset(AppAssets.arrowBack),
                    )
                  : InkWell(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: SvgPicture.asset(
                        AppAssets.forwardArrow,
                        colorFilter:
                            const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                        width: 25.w,
                        height: 25.w,
                      ),
                    ),
            ),
      body: SingleChildScrollView(
        child: BlocProvider(
          create: (BuildContext context) =>
              CAdreessCubit()..getCAdreess(status: widget.status),
          child: BlocBuilder<CAdreessCubit, CAdreessStates>(
            builder: (context, states) {
              // if (condition) {}
              if (states is CAdreessLoadingStates) {
                return const Center(
                  child: CircularProgressIndicator(
                    color: TColor.mainColor,
                  ),
                );
              } else if (states is CAdreessSuccessStates) {
                if (states.cAdreessModels2!.isEmpty) {
                  return SizedBox(
                    width: 600.w,
                    height: 300.w,
                    child: Center(
                      child: CustomText(
                        text: AppStrings.requestAbsencesNotFound.tr(),
                        fontSize: 17,
                        fontW: FontWeight.w600,
                      ),
                    ),
                  );
                } else {
                  return Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Container(
                          margin: const EdgeInsets.symmetric(horizontal: 8.0),
                          clipBehavior: Clip.antiAlias,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(15.0),
                          ),
                          child: Table(
                            columnWidths: const {
                              0: FlexColumnWidth(3),
                              1: FlexColumnWidth(2),
                              2: FlexColumnWidth(2),
                              3: FlexColumnWidth(1),
                            },
                            border: TableBorder.all(
                              color: TColor.tabColors,
                              borderRadius: BorderRadius.circular(15.0),
                            ),
                            children: List.generate(
                                1 + states.cAdreessModels2!.length, (index) {
                              if (index == 0) {
                                return BuildTableRowWidget(
                                  cell: [
                                    AppStrings.studentName.tr(),
                                    AppStrings.parent.tr(),
                                    AppStrings.newAddress.tr(),
                                    AppStrings.options.tr(),
                                  ],
                                  header: true,
                                ).build(context);
                              } else {
                                final newAbsences =
                                    states.cAdreessModels2![index - 1];
                                return BuildTableRowWidget(
                                    cell: [
                                      newAbsences?.studentName ?? "",
                                      newAbsences?.parentName ?? "",
                                      newAbsences?.address,
                                      Icons.more_horiz,
                                    ],
                                    onTapDown: (position) {
                                      Helpers.customAddressRequestShowDialog(
                                        context,
                                        position: position.globalPosition,
                                        onTapShow: () {
                                          Navigator.of(context)
                                            ..pop()
                                            ..pushNamed(
                                              PathRouteName
                                                  .requestChangeAddress,
                                              arguments: newAbsences,
                                            );
                                        },
                                      );
                                      // NotificationsCubit.get(context)
                                      //     .getSuperVisorsFcmToken(
                                      //         busId: newAbsences?.bus_id
                                      //             .toString());
                                    }).build(context);
                              }
                            }),
                          ),
                        ),
                      ),
                      20.verticalSpace,
                      PageNumberWidget(
                        lastPage: states.last_page,
                        currentPage: states.curentPage,
                        type: "ChangeAddressrequest",
                        status: widget.status,
                      ),
                      30.verticalSpace,
                    ],
                  );
                }
              } else if (states is CAdreessErrorStates) {
                if (states.error == "order Absence not found") {
                  return SizedBox(
                    width: 600.w,
                    height: 300.w,
                    child: Center(
                      child: CustomText(
                        text: AppStrings.orderAbsenceNotFound.tr(),
                        fontSize: 17,
                        fontW: FontWeight.w600,
                      ),
                    ),
                  );
                } else {
                  return SizedBox(
                    width: 600.w,
                    height: 300.w,
                    child: Center(
                      child: CustomText(
                        text: AppStrings.requestAbsencesNotFound.tr(),
                        fontSize: 17,
                        fontW: FontWeight.w600,
                      ),
                    ),
                  );
                }
              } else {
                return const SizedBox();
              }
            },
          ),
        ),
      ),
    );
  }
}
