import 'package:bus/bloc/change_address_cubit/change_address_cubit.dart';
import 'package:bus/bloc/change_address_cubit/change_address_states.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../config/theme_colors.dart';
import 'package:bus/translations/local_keys.g.dart';
import '../../../utils/assets_utils.dart';
import '../../../widgets/custom_appbar.dart';
import '../../../widgets/page_number_widget.dart';
import '../../custom_widgets/custom_text.dart';

class RequestsAddressChangeScreen extends StatefulWidget {
  final String? status;
  final String? appBarTitle;
  final bool hideAppBar;
  static const String routeName = PathRouteName.requestsAddressChange;

  const RequestsAddressChangeScreen({
    Key? key,
    this.status,
    this.appBarTitle,
    this.hideAppBar = false,
  }) : super(key: key);

  @override
  State<RequestsAddressChangeScreen> createState() =>
      _RequestsAddressChangeScreenState();
}

class _RequestsAddressChangeScreenState
    extends State<RequestsAddressChangeScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: widget.hideAppBar
          ? null
          : CustomAppBar(
              titleWidget: CustomText(
                text: widget.appBarTitle,
                fontSize: 18,
                textAlign: TextAlign.center,
                fontW: FontWeight.w600,
                color: TColor.white,
              ),
              leftWidget: context.locale.toString() == "ar"
                  ? InkWell(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: SvgPicture.asset(AppAssets.arrowBack),
                    )
                  : InkWell(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: SvgPicture.asset(
                        AppAssets.forwardArrow,
                        colorFilter:
                            const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                        width: 25.w,
                        height: 25.w,
                      ),
                    ),
            ),
      body: SingleChildScrollView(
        child: BlocProvider(
          create: (BuildContext context) =>
              CAdreessCubit()..getCAdreess(status: widget.status),
          child: BlocBuilder<CAdreessCubit, CAdreessStates>(
            builder: (context, states) {
              // if (condition) {}
              if (states is CAdreessLoadingStates) {
                return const Center(
                  child: CircularProgressIndicator(
                    color: TColor.mainColor,
                  ),
                );
              } else if (states is CAdreessSuccessStates) {
                if (states.cAdreessModels2!.isEmpty) {
                  return SizedBox(
                    width: 600.w,
                    height: 300.w,
                    child: Center(
                      child: CustomText(
                        text: AppStrings.requestAbsencesNotFound.tr(),
                        fontSize: 17,
                        fontW: FontWeight.w600,
                      ),
                    ),
                  );
                } else {
                  return _buildSuccessState(states);
                }
              } else if (states is CAdreessErrorStates) {
                if (states.error == "order Absence not found") {
                  return SizedBox(
                    width: 600.w,
                    height: 300.w,
                    child: Center(
                      child: CustomText(
                        text: AppStrings.orderAbsenceNotFound.tr(),
                        fontSize: 17,
                        fontW: FontWeight.w600,
                      ),
                    ),
                  );
                } else {
                  return SizedBox(
                    width: 600.w,
                    height: 300.w,
                    child: Center(
                      child: CustomText(
                        text: AppStrings.requestAbsencesNotFound.tr(),
                        fontSize: 17,
                        fontW: FontWeight.w600,
                      ),
                    ),
                  );
                }
              } else {
                return const SizedBox();
              }
            },
          ),
        ),
      ),
    );
  }

  // Build success state with card-based design
  Widget _buildSuccessState(CAdreessSuccessStates state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title and info
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          child: Row(
            children: [
              CustomText(
                text: "عدد الطلبات: ${state.cAdreessModels2!.length}",
                fontSize: 14,
                fontW: FontWeight.w500,
                color: Colors.grey.shade700,
              ),
              const Spacer(),
              _buildStatusIndicator(widget.status),
            ],
          ),
        ),

        SizedBox(height: 16.h),

        // Requests list
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            itemCount: state.cAdreessModels2!.length,
            itemBuilder: (context, index) {
              final item = state.cAdreessModels2![index];
              return _buildAddressCard(item!);
            },
          ),
        ),

        // Pagination
        Padding(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          child: PageNumberWidget(
            lastPage: state.last_page,
            currentPage: state.curentPage,
            type: "ChangeAddressrequest",
            status: widget.status,
          ),
        ),
      ],
    );
  }

  // Build address card matching temporary address design
  Widget _buildAddressCard(CAdreessModels item) {
    // Determine status color and icon
    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (item.status) {
      case 0:
        statusColor = Colors.orange;
        statusIcon = Icons.pending_outlined;
        statusText = AppStrings.newS.tr();
        break;
      case 1:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle_outline;
        statusText = AppStrings.accepted.tr();
        break;
      case 2:
        statusColor = Colors.red;
        statusIcon = Icons.cancel_outlined;
        statusText = AppStrings.refused.tr();
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help_outline;
        statusText = item.statusText?.text ?? '';
    }

    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      elevation: 3,
      shadowColor: Colors.black26,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
        side: BorderSide(
          color: Color.fromRGBO(
            statusColor.r.toInt(),
            statusColor.g.toInt(),
            statusColor.b.toInt(),
            0.3
          ),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () => _navigateToDetailsScreen(context, item),
        borderRadius: BorderRadius.circular(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status bar at top
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 8.h),
              decoration: BoxDecoration(
                color: Color.fromRGBO(
                  statusColor.r.toInt(),
                  statusColor.g.toInt(),
                  statusColor.b.toInt(),
                  0.15
                ),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.r),
                  topRight: Radius.circular(16.r),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    statusIcon,
                    size: 18,
                    color: statusColor,
                  ),
                  SizedBox(width: 6.w),
                  CustomText(
                    text: statusText,
                    fontSize: 14,
                    fontW: FontWeight.w600,
                    color: statusColor,
                  ),
                ],
              ),
            ),

            // Card content
            Padding(
              padding: EdgeInsets.all(16.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Address
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(
                        Icons.location_on,
                        size: 22,
                        color: TColor.mainColor,
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: CustomText(
                          text: item.address ?? "عنوان غير معروف",
                          fontSize: 16,
                          fontW: FontWeight.w600,
                          color: TColor.black,
                          maxLine: 2,
                        ),
                      ),
                    ],
                  ),

                  // Student info
                  SizedBox(height: 12.h),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(
                        Icons.person,
                        size: 22,
                        color: TColor.mainColor,
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: CustomText(
                          text: "الطالب: ${item.studentName}",
                          fontSize: 14,
                          fontW: FontWeight.w500,
                          color: Colors.grey.shade800,
                          maxLine: 2,
                        ),
                      ),
                    ],
                  ),

                  // Parent info
                  if (item.parentName != null && item.parentName!.isNotEmpty) ...[
                    SizedBox(height: 8.h),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Icon(
                          Icons.family_restroom,
                          size: 22,
                          color: TColor.mainColor,
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: CustomText(
                            text: "ولي الأمر: ${item.parentName}",
                            fontSize: 14,
                            fontW: FontWeight.w500,
                            color: Colors.grey.shade800,
                            maxLine: 2,
                          ),
                        ),
                      ],
                    ),
                  ],

                  // Bus info
                  SizedBox(height: 8.h),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(
                        Icons.directions_bus,
                        size: 22,
                        color: TColor.mainColor,
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: CustomText(
                          text: "الحافلة: ${item.busName}",
                          fontSize: 14,
                          fontW: FontWeight.w500,
                          color: Colors.grey.shade800,
                          maxLine: 2,
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 16.h),

                  // Additional info container
                  Container(
                    padding: EdgeInsets.all(12.r),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            _buildInfoItem(
                              Icons.school,
                              "المدرسة: ${item.schoolName}",
                              flex: 1,
                              iconColor: Colors.blue.shade700,
                            ),
                          ],
                        ),
                        if (item.gradeName.isNotEmpty) ...[
                          SizedBox(height: 8.h),
                          Row(
                            children: [
                              _buildInfoItem(
                                Icons.grade,
                                "الصف: ${item.gradeName}",
                                flex: 1,
                                iconColor: Colors.purple.shade700,
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Bottom action bar
            Container(
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(16.r),
                  bottomRight: Radius.circular(16.r),
                ),
                border: Border(
                  top: BorderSide(
                    color: Colors.grey.shade200,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  // Request ID
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
                      child: Row(
                        children: [
                          Icon(
                            Icons.tag,
                            size: 14,
                            color: Colors.grey.shade600,
                          ),
                          SizedBox(width: 4.w),
                          Expanded(
                            child: CustomText(
                              text: "رقم الطلب: ${item.id}",
                              fontSize: 12,
                              fontW: FontWeight.w400,
                              color: Colors.grey.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Details button
                  Container(
                    decoration: BoxDecoration(
                      border: Border(
                        right: BorderSide(
                          color: Colors.grey.shade200,
                          width: 1,
                        ),
                      ),
                    ),
                    child: TextButton.icon(
                      onPressed: () => _navigateToDetailsScreen(context, item),
                      icon: const Icon(Icons.arrow_forward_ios, size: 14),
                      label: const CustomText(
                        text: "التفاصيل",
                        fontSize: 14,
                        fontW: FontWeight.w500,
                      ),
                      style: TextButton.styleFrom(
                        foregroundColor: TColor.mainColor,
                        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.zero,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Info item widget
  Widget _buildInfoItem(IconData icon, String text, {int flex = 1, Color? iconColor}) {
    return Expanded(
      flex: flex,
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: iconColor ?? Colors.grey.shade600,
          ),
          SizedBox(width: 4.w),
          Expanded(
            child: CustomText(
              text: text,
              fontSize: 14,
              fontW: FontWeight.w400,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }

  // Status indicator widget
  Widget _buildStatusIndicator(String? status) {
    String statusText;
    Color statusColor;

    switch (status) {
      case 'new':
        statusText = "طلبات جديدة";
        statusColor = Colors.orange;
        break;
      case 'accept':
        statusText = "طلبات مقبولة";
        statusColor = Colors.green;
        break;
      case 'unaccept':
        statusText = "طلبات مرفوضة";
        statusColor = Colors.red;
        break;
      default:
        statusText = "جميع الطلبات";
        statusColor = TColor.mainColor;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: Color.fromRGBO(
          statusColor.r.toInt(),
          statusColor.g.toInt(),
          statusColor.b.toInt(),
          0.1
        ),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8.w,
            height: 8.w,
            decoration: BoxDecoration(
              color: statusColor,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 6.w),
          CustomText(
            text: statusText,
            fontSize: 12,
            fontW: FontWeight.w500,
            color: statusColor,
          ),
        ],
      ),
    );
  }

  // Navigate to details screen
  void _navigateToDetailsScreen(BuildContext context, CAdreessModels item) {
    Navigator.pushNamed(
      context,
      PathRouteName.requestChangeAddress,
      arguments: item,
    );
  }
}
